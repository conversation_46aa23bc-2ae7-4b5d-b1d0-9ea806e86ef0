{"name": "cnb-runner", "version": "1.0.0", "description": "Playwright automation for Cnb WebIDE", "main": "index.js", "scripts": {"login": "node src/login.js", "execute": "node src/execute-command.js", "scheduler": "node src/scheduler.js", "web-server": "node src/web-server.js", "start": "node src/start-services.js", "test-kv": "node src/test-cloudflare-kv.js", "docker-build": "docker build --progress=plain -t zhezzma/cnb-runner:latest .", "docker-push": "docker push zhezzma/cnb-runner:latest"}, "keywords": ["playwright", "automation", "webide"], "author": "", "license": "ISC", "type": "module", "dependencies": {"@hono/node-server": "^1.13.7", "dotenv": "^16.5.0", "hono": "^4.6.12", "playwright": "^1.52.0"}}