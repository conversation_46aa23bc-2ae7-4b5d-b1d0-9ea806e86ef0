# 环境变量配置示例文件
# 复制此文件为 .env 并根据需要修改配置

# Project URL - 如果设置了此环境变量，将覆盖 config.js 中的默认 projectUrl
# PROJECT_URL=https://your-custom-webide-url.com/edit

# 调度器时间间隔（毫秒）- 如果设置了此环境变量，将覆盖 config.js 中的默认值
# 默认为 600000 毫秒（10分钟）
# SCHEDULER_INTERVAL=600000

# 浏览器无头模式 - 如果设置了此环境变量，将覆盖 config.js 中的默认值
# 默认为 true（无头模式），设置为 false 可以显示浏览器界面
# HEADLESS=false

# Cookies - 如果设置了此环境变量，将优先使用环境变量中的cookies而不是cookies.json文件
# 格式为JSON字符串，包含从浏览器导出的cookies数组
# COOKIES='[{"name":"session","value":"abc123","domain":".example.com","path":"/"}]'

# Cloudflare KV 配置 - 用于远程存储和读取 cookies
# CF_ACCOUNT_ID=your_cloudflare_account_id
# CF_NAMESPACE_ID=your_kv_namespace_id
# CF_API_TOKEN=your_cloudflare_api_token
# CF_COOKIE=cookies  # 远程 cookies 键名，如果设置了此环境变量，将从 Cloudflare KV 读取 cookies

# HuggingFace 配置 - 用于重启远程服务
# HF_TOKEN=your_huggingface_token  # HuggingFace API Token，用于重启远程空间服务

# 示例：
# PROJECT_URL=https://cnb-too-1islbrcti-001.cnb.space/?folder=/workspace
# SCHEDULER_INTERVAL=300000  # 5分钟
# SCHEDULER_INTERVAL=900000  # 15分钟

# Cloudflare KV 使用说明：
# 1. 在 Cloudflare 控制台创建 KV 命名空间
# 2. 获取账户 ID、命名空间 ID 和 API Token
# 3. 设置上述环境变量
# 4. 运行 npm run login 登录后，cookies 会自动上传到 KV
# 5. 设置 CF_COOKIE 环境变量后，程序会从 KV 读取 cookies
#
# HuggingFace 远程服务重启说明：
# 1. 在 HuggingFace 获取 API Token
# 2. 设置 HF_TOKEN 环境变量
# 3. 运行 npm run login 登录并上传 cookies 后，会自动重启远程服务
# 4. 默认重启的空间：cnb-runner-lmarena, cnb-runner
#
# Cookie 优先级（从高到低）：
# 1. CF_COOKIE（从 Cloudflare KV 读取）
# 2. COOKIES（直接从环境变量读取）
# 3. cookies.json（从本地文件读取）
