import fs from 'fs';
import path from 'path';
import { info, error } from './logger.js';
import { downloadCookiesFromKV } from './cloudflare-kv-utils.js';

/**
 * 创建人类可读的时间戳
 * @returns {string} 格式化的时间戳 YYYY-MM-DD_HH-MM-SS
 */
export function getHumanReadableTimestamp() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day}_${hours}-${minutes}-${seconds}`;
}

/**
 * 确保截图目录存在
 * @param {string} dir - 目录路径
 */
export function ensureScreenshotDirectory(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    info(`创建截图目录: ${dir}`);
  }
}

/**
 * 检查 Cookie 是否可用（环境变量或文件）
 * @param {string} cookieFile - Cookie 文件路径
 * @param {string} cookiesFromEnv - 环境变量中的cookies
 * @returns {boolean} Cookie是否可用
 */
export function checkCookieAvailability(cookieFile, cookiesFromEnv) {
  // 最高优先级：CF_COOKIE 环境变量
  if (process.env.CF_COOKIE) {
    info('发现环境变量CF_COOKIE，将从 Cloudflare KV 读取 cookies');
    return true;
  }

  // 第二优先级：COOKIES 环境变量
  if (cookiesFromEnv) {
    info('发现环境变量COOKIES，将使用环境变量中的cookies');
    try {
      JSON.parse(cookiesFromEnv);
      return true;
    } catch (err) {
      error('环境变量COOKIES格式无效:', err.message);
      info('将尝试使用cookie文件...');
    }
  }

  // 最低优先级：检查cookie文件
  if (!fs.existsSync(cookieFile)) {
    error(`Cookie文件不存在: ${cookieFile}`);
    info('请先运行 npm run login 进行登录，或设置环境变量COOKIES/CF_COOKIE');
    return false;
  }
  return true;
}

/**
 * 检查 Cookie 文件是否存在（保持向后兼容）
 * @param {string} cookieFile - Cookie 文件路径
 * @returns {boolean} 文件是否存在
 */
export function checkCookieFile(cookieFile) {
  if (!fs.existsSync(cookieFile)) {
    error(`Cookie文件不存在: ${cookieFile}`);
    info('请先运行 npm run login 进行登录');
    return false;
  }
  return true;
}

/**
 * 读取并解析 Cookie（优先使用环境变量）
 * @param {string} cookieFile - Cookie 文件路径
 * @param {string} cookiesFromEnv - 环境变量中的cookies
 * @param {string} cfCookie - 环境变量中的cookies
 * @returns {Array} Cookie 数组
 */
export async function loadCookies(cookieFile, cookiesFromEnv, cfCookie) {
  try {
    // 最高优先级：CF_COOKIE 环境变量，从 Cloudflare KV 读取
    if (cfCookie) {
      info('检测到 CF_COOKIE 环境变量，从 Cloudflare KV 加载 cookies...');
      const cookies = await downloadCookiesFromKV();
      if (cookies && cookies.length > 0) {
        info(`已从 Cloudflare KV 加载 ${cookies.length} 个 cookies`);
        return cookies;
      } else {
        info('从 Cloudflare KV 加载 cookies 失败，尝试其他方式...');
      }
    }

    // 第二优先级：COOKIES 环境变量
    if (cookiesFromEnv) {
      info('从环境变量COOKIES加载cookies...');
      const cookies = JSON.parse(cookiesFromEnv);
      info(`已从环境变量加载 ${cookies.length} 个cookies`);
      return cookies;
    }

    // 最低优先级：使用文件
    if (fs.existsSync(cookieFile)) {
      info(`从文件加载cookies: ${cookieFile}`);
      const cookies = JSON.parse(fs.readFileSync(cookieFile, 'utf8'));
      info(`已从文件加载 ${cookies.length} 个cookies`);
      return cookies;
    }
    return [];
  } catch (err) {
    error('读取 Cookie 失败:', err);
    throw err;
  }
}

/**
 * 保存截图
 * @param {Object} page - Playwright 页面对象
 * @param {string} screenshotDir - 截图目录
 * @param {string} prefix - 文件名前缀
 * @returns {string} 截图文件路径
 */
export async function saveScreenshot(page, screenshotDir, prefix = 'screenshot') {
  try {
    ensureScreenshotDirectory(screenshotDir);
    const screenshotPath = path.join(screenshotDir, `${prefix}.png`);

    // 设置截图超时时间为15秒
    await page.screenshot({
      path: screenshotPath,
      timeout: 15000
    });

    info(`截图已保存: ${screenshotPath}`);
    return screenshotPath;
  } catch (err) {
    error(`截图保存失败: ${err.message}`);
    // 返回null表示截图失败，但不抛出异常
    return null;
  }
}


// 等待用户输入的辅助函数
export async function waitForUserInput() {
  const { default: readline } = await import('readline');
  return new Promise((resolve) => {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    rl.question('', () => {
      rl.close();
      resolve();
    });
  });
}