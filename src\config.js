// 配置文件
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

const config = {

  // WebIDE URL - 可以通过环境变量 PROJECT_URL 覆盖
  projectUrl: process.env.PROJECT_URL || '',

  // Git 分支 - 可以通过环境变量 BRANCH 覆盖
  branch: process.env.BRANCH || 'main',

  // 调度器时间间隔（毫秒）- 可以通过环境变量 SCHEDULER_INTERVAL 覆盖
  // 默认为 7 分钟 (7 * 60 * 1000 = 600000 毫秒)
  schedulerInterval: parseInt(process.env.SCHEDULER_INTERVAL) || 7 * 60 * 1000,

  // 是否在服务启动后立即执行一次命令 - 可以通过环境变量 RUN_COMMAND_ON_START 覆盖
  runCommandOnStart: (process.env.RUN_COMMAND_ON_START || 'true').toLowerCase() === 'true',

  // 工作空间清理配置
  workspaceCleanup: {
    // 是否启用自动清理关闭的工作空间 - 可以通过环境变量 ENABLE_WORKSPACE_CLEANUP 覆盖
    enabled: (process.env.ENABLE_WORKSPACE_CLEANUP || 'true').toLowerCase() === 'true',

    // 清理间隔（毫秒）- 可以通过环境变量 WORKSPACE_CLEANUP_INTERVAL 覆盖
    // 默认为 60 分钟 (60 * 60 * 1000 = 1800000 毫秒)
    interval: parseInt(process.env.WORKSPACE_CLEANUP_INTERVAL) || 15 * 60 * 60 * 1000,

    // 是否在调度器启动时立即执行一次清理
    runOnStart: (process.env.WORKSPACE_CLEANUP_ON_START || 'true').toLowerCase() === 'true'
  },

  // Cookie文件路径
  cookieFile: './cookies.json',

  // Cookie环境变量 - 如果设置了此环境变量，将优先使用环境变量中的cookies
  cookiesFromEnv: process.env.COOKIES,

  // Cloudflare KV 配置 - 用于远程存储和读取 cookies
  cloudflareKV: {
    // Cloudflare 账户 ID
    accountId: process.env.CF_ACCOUNT_ID,
    // KV 命名空间 ID
    namespaceId: process.env.CF_NAMESPACE_ID,
    // Cloudflare API Token
    apiToken: process.env.CF_API_TOKEN,
    // 远程 cookies 键名 - 通过环境变量 CF_COOKIE 指定
    cookieKey: process.env.CF_COOKIE
  },

  // HuggingFace 配置 - 用于重启远程服务
  huggingface: {
    // HuggingFace API Token
    token: process.env.HF_TOKEN,
    // 需要重启的空间列表
    spaces: [],//["cnb-runner"]
  },

  // 浏览器配置
  browserOptions: {
    // 默认无头模式，可通过环境变量 HEADLESS=false 设置为有头模式
    // 支持 false/False/FALSE 等不同大小写形式
    headless: (process.env.HEADLESS || 'true').toLowerCase() !== 'false',
    slowMo: 100,     // 操作间隔时间（毫秒）
    timeout: 30000,   // 超时时间（毫秒）
    executablePath: process.env.PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH,
  },

  // 要执行的命令
  command: process.env.COMMAND || 'date  && service cron start ',

  // 截图保存目录
  screenshotDir: './screenshots',

  // 等待时间配置（毫秒）
  waitTimes: {
    pageLoad: 5000,      // 页面加载等待时间
    terminalOpen: 3000,  // 终端打开等待时间
    commandExecution: 2000 // 命令执行等待时间
  },

  // 页面选择器（需要根据实际登录页面调整）
  selectors: {
    // 这些选择器需要根据实际的登录页面进行调整
    editor: '.monaco-grid-view',
    terminals: [
      '.terminal',
      // '.xterm',
      // '.console',
      // '.terminal-container',
      //'.xterm-screen',
      // '.monaco-workbench .part.panel .terminal',
      // '[data-testid="terminal"]',
      // '.integrated-terminal'
    ],
  }
};

export default config;
