import config from './config.js';
import { fileURLToPath } from 'url';
import path from 'path';
import { getHumanReadableTimestamp } from './utils/common-utils.js';
import { createBrowserSession, createNewContextAndPage, navigateToWebIDE, executeCommandFlow, deleteClosedWorkspaces } from './utils/webide-utils.js';
import { info, error } from './utils/logger.js';

// 执行单次命令的函数
async function executeCommandOnce(page) {
  info(`[${getHumanReadableTimestamp()}] 开始执行命令...`);
  executeCommandFlow(page, 'scheduler');
}

// 执行工作空间清理的函数
async function executeWorkspaceCleanup() {
  if (!config.workspaceCleanup.enabled) {
    return;
  }

  try {
    info(`[${getHumanReadableTimestamp()}] 开始清理关闭的工作空间...`);
    const result = await deleteClosedWorkspaces();

    if (result.deletedCount > 0) {
      info(`[${getHumanReadableTimestamp()}] 工作空间清理完成: ${result.message}`);
    } else {
      info(`[${getHumanReadableTimestamp()}] 工作空间清理完成: 没有需要清理的工作空间`);
    }
  } catch (err) {
    error(`[${getHumanReadableTimestamp()}] 工作空间清理失败:`, err);
  }
}

// 主调度器函数
async function startScheduler() {

  info(`[${getHumanReadableTimestamp()}] 启动调度器...`);
  const intervalSeconds = Math.round(config.schedulerInterval / 1000);
  info(`调度器将每${intervalSeconds}秒执行一次命令以防止编辑器休眠`);

  // 工作空间清理配置信息
  if (config.workspaceCleanup.enabled) {
    const cleanupIntervalMinutes = Math.round(config.workspaceCleanup.interval / (60 * 1000));
    info(`工作空间清理已启用，将每${cleanupIntervalMinutes}分钟清理一次关闭的工作空间`);
  } else {
    info('工作空间清理已禁用');
  }

  let browser;
  let currentContext = null;
  try {
    // 创建浏览器会话
    let { browser: browserInstance, context, page } = await createBrowserSession(config.cookieFile, config.cookiesFromEnv,config.cloudflareKV.cookieKey);
    browser = browserInstance;
    currentContext = context;

    // 导航到WebIDE页面
    page = await navigateToWebIDE(page);

    // 根据配置决定是否立即执行一次命令
    if (config.runCommandOnStart) {
      info(`[${getHumanReadableTimestamp()}] 配置启用启动时执行命令，立即执行一次...`);
      await executeCommandOnce(page);
    } else {
      info(`[${getHumanReadableTimestamp()}] 配置禁用启动时执行命令，跳过立即执行`);
    }

    // 如果配置了启动时清理，立即执行一次工作空间清理
    if (config.workspaceCleanup.runOnStart) {
      await executeWorkspaceCleanup();
    }

    // 设置命令执行定时器
    const commandIntervalId = setInterval(async () => {
      try {
        // 关闭旧的context
        if (currentContext) {
          await currentContext.close();
        }

        // 创建新的context和page，重新加载cookies
        info(`[${getHumanReadableTimestamp()}] 创建新的上下文和页面，重新加载cookies...`);
        const { context: newContext, page: newPage } = await createNewContextAndPage(
          browser,
          config.cookieFile,
          config.cookiesFromEnv,
          config.cloudflareKV.cookieKey
        );
        currentContext = newContext;

        // 导航到WebIDE页面
        const page = await navigateToWebIDE(newPage);

        // 执行命令
        await executeCommandOnce(page);
      } catch (err) {
        error(`[${getHumanReadableTimestamp()}] 定时任务执行失败:`, err);
      }
    }, config.schedulerInterval);

    // 设置工作空间清理定时器（如果启用）
    let workspaceCleanupIntervalId = null;
    if (config.workspaceCleanup.enabled) {
      workspaceCleanupIntervalId = setInterval(async () => {
        await executeWorkspaceCleanup();
      }, config.workspaceCleanup.interval);
    }

    info(`[${getHumanReadableTimestamp()}] 调度器已启动，将每${intervalSeconds}秒执行一次命令`);
    info('按 Ctrl+C 停止调度器');

    // 清理函数
    const cleanup = async () => {
      info(`\n[${getHumanReadableTimestamp()}] 正在关闭调度器...`);
      clearInterval(commandIntervalId);
      if (workspaceCleanupIntervalId) {
        clearInterval(workspaceCleanupIntervalId);
      }
      if (currentContext) {
        await currentContext.close();
      }
      if (browser) {
        await browser.close();
      }
      info('调度器已停止，浏览器已关闭');
      process.exit(0);
    };

    // 监听进程退出信号
    process.on('SIGINT', cleanup);
    process.on('SIGTERM', cleanup);

  } catch (err) {
    error(`[${getHumanReadableTimestamp()}] 调度器启动失败:`, err);
    if (browser) {
      await browser.close();
    }
  }
}

// 运行调度器
const __filename = fileURLToPath(import.meta.url);
const scriptPath = path.resolve(process.argv[1]);

if (path.resolve(__filename) === scriptPath) {
  startScheduler().catch(error);
}

export { startScheduler };
