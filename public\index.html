<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cnb Runner - 日志查看器</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 2em;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }

        .controls {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .controls label {
            font-weight: bold;
        }

        .controls select,
        .controls button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .controls button {
            background: #667eea;
            color: white;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }

        .controls button:hover {
            background: #5a6fd8;
        }

        .controls button.danger {
            background: #dc3545;
        }

        .controls button.danger:hover {
            background: #c82333;
        }

        .workspace-section {
            padding: 20px;
            border-bottom: 1px solid #eee;
        }

        .workspace-section h2 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.2em;
        }

        .workspace-controls {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .workspace-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .workspace-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .workspace-item:last-child {
            border-bottom: none;
        }

        .workspace-info {
            flex: 1;
        }

        .workspace-slug {
            font-weight: bold;
            color: #333;
        }

        .workspace-status {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }

        .workspace-status.running {
            background: #d4edda;
            color: #155724;
        }

        .workspace-status.closed {
            background: #f8d7da;
            color: #721c24;
        }

        .workspace-details {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }

        .log-container {
            padding: 20px;
            max-height: 600px;
            overflow-y: auto;
        }

        .log-entry {
            margin-bottom: 8px;
            padding: 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            border-left: 3px solid #ddd;
        }

        .log-entry.info {
            background-color: #f8f9fa;
            border-left-color: #28a745;
        }

        .log-entry.error {
            background-color: #fff5f5;
            border-left-color: #dc3545;
            color: #721c24;
        }

        .log-entry.warn {
            background-color: #fffbf0;
            border-left-color: #ffc107;
            color: #856404;
        }

        .timestamp {
            color: #6c757d;
            font-weight: bold;
        }

        .level {
            font-weight: bold;
            margin: 0 8px;
        }

        .level.info {
            color: #28a745;
        }

        .level.error {
            color: #dc3545;
        }

        .level.warn {
            color: #ffc107;
        }

        .message {
            word-break: break-word;
        }

        .status {
            padding: 10px 20px;
            background: #e9ecef;
            border-top: 1px solid #ddd;
            font-size: 14px;
            color: #6c757d;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .empty {
            text-align: center;
            padding: 40px;
            color: #6c757d;
            font-style: italic;
        }

        .screenshot-section {
            padding: 20px;
            border-bottom: 1px solid #eee;
            text-align: center;
        }

        .screenshot-section h2 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.2em;
        }

        .screenshot-section img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .cursor-pointer{
            cursor: pointer;
        }

        .config-item {
            padding: 12px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .config-item:last-child {
            border-bottom: none;
        }

        .config-label {
            font-weight: bold;
            color: #333;
            min-width: 150px;
        }

        .config-value {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid #e9ecef;
            word-break: break-all;
            flex: 1;
            margin-left: 10px;
        }

        .config-value.empty {
            color: #6c757d;
            font-style: italic;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Cnb Runner</h1>
            <p>日志查看器 & 工作空间管理器</p>
        </div>

        <div class="status" style="display: none;" id="status">
            准备就绪
        </div>

        <!-- 系统配置区域 -->
        <div class="workspace-section">
            <h2>⚙️ 系统配置</h2>
            <div class="workspace-controls">
                <button onclick="fetchSystemConfig()" class="cursor-pointer">🔄 刷新配置</button>
            </div>
            <div class="workspace-list" id="configContainer">
                <div class="loading">正在加载配置信息...</div>
            </div>
        </div>

        <!-- 工作空间管理区域 -->
        <div class="workspace-section">
            <h2>🗂️ 工作空间管理</h2>
            <div class="workspace-controls">
                <button onclick="refreshWorkspaces()" class="cursor-pointer">🔄 刷新工作空间</button>
                <button onclick="deleteClosedWorkspaces()" class="danger cursor-pointer">🗑️ 删除关闭的工作空间</button>
            </div>
            <div class="workspace-list" id="workspaceList">
                <div class="loading">正在加载工作空间...</div>
            </div>
        </div>

        <div class="screenshot-section">
            <h2>📸 最新截图</h2>
            <img src="/screenshots/scheduler.png" alt="Scheduler Screenshot"
                onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
            <div style="display: none; color: #6c757d; font-style: italic;">截图文件不存在或无法加载</div>
        </div>

        <div class="controls">
            <label for="lines">日志显示行数:</label>
            <select id="lines">
                <option value="50">50 行</option>
                <option value="100" selected>100 行</option>
                <option value="200">200 行</option>
                <option value="500">500 行</option>
                <option value="1000">1000 行</option>
            </select>

            <button onclick="refreshLogs()">🔄 刷新日志</button>
            <button onclick="toggleAutoRefresh()">⏱️ 自动刷新</button>
            <button onclick="clearLogs()">🗑️ 清空显示</button>
        </div>

        <div class="log-container" id="logContainer">
            <div class="loading">正在加载日志...</div>
        </div>



    </div>

    <script>
        let autoRefreshInterval = null;
        let isAutoRefresh = false;

        // 系统配置管理功能
        async function fetchSystemConfig() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();

                if (data.success) {
                    displaySystemConfig(data);
                    updateStatus(`系统配置已加载 - ${new Date().toLocaleString()}`);
                } else {
                    updateStatus('获取系统配置失败: ' + data.error);
                }
            } catch (error) {
                updateStatus('获取系统配置时发生网络错误: ' + error.message);
            }
        }

        function displaySystemConfig(statusData) {
            const container = document.getElementById('configContainer');

            const config = statusData.config || {};
            const uptime = Math.floor(statusData.uptime || 0);
            const memory = statusData.memory || {};

            // 格式化运行时间
            const hours = Math.floor(uptime / 3600);
            const minutes = Math.floor((uptime % 3600) / 60);
            const seconds = uptime % 60;
            const uptimeStr = `${hours}小时 ${minutes}分钟 ${seconds}秒`;

            // 格式化内存使用
            const memoryUsage = `${Math.round(memory.rss / 1024 / 1024)}MB`;
            const configHTML = `
                <div class="config-item">
                    <div class="config-label">项目URL:</div>
                    <div class="config-value ${config.projectUrl ? '' : 'empty'}">
                        ${config.projectUrl.replace(`https://cnb.cool/`,'') || '未配置'}
                    </div>
                </div>
                <div class="config-item">
                    <div class="config-label">调度器间隔:</div>
                    <div class="config-value">
                        ${Math.round(config.schedulerInterval / 1000 / 60)}分钟
                    </div>
                </div>
                <div class="config-item">
                    <div class="config-label">浏览器模式:</div>
                    <div class="config-value">
                        ${config.headless ? '无头模式' : '有头模式'}
                    </div>
                </div>
                <div class="config-item">
                    <div class="config-label">系统状态:</div>
                    <div class="config-value">
                        ${statusData.status || 'unknown'}
                    </div>
                </div>
                <div class="config-item">
                    <div class="config-label">运行时间:</div>
                    <div class="config-value">
                        ${uptimeStr}
                    </div>
                </div>
               <!-- <div class="config-item">
                    <div class="config-label">内存使用:</div>
                    <div class="config-value">
                        ${memoryUsage}
                    </div>
                </div> -->
            `;

            container.innerHTML = configHTML;
        }

        // 工作空间管理功能
        async function fetchWorkspaces() {
            try {
                const response = await fetch('/api/workspaces');
                const data = await response.json();

                if (data.success) {
                    displayWorkspaces(data.workspaces);
                    updateStatus(`已加载 ${data.workspaces.length} 个工作空间 - ${new Date().toLocaleString()}`);
                } else {
                    updateStatus('获取工作空间失败: ' + data.error);
                }
            } catch (error) {
                updateStatus('获取工作空间时发生网络错误: ' + error.message);
            }
        }

        function displayWorkspaces(workspaces) {
            const container = document.getElementById('workspaceList');

            if (workspaces.length === 0) {
                container.innerHTML = '<div class="empty">暂无工作空间数据</div>';
                return;
            }

            const workspaceHTML = workspaces.map(workspace => {
                const statusClass = workspace.status === 'running' ? 'running' : 'closed';
                const createTime = new Date(workspace.create_time).toLocaleString();

                return `
                    <div class="workspace-item">
                        <div class="workspace-info">
                            <div class="workspace-slug">${workspace.slug}</div>
                            <div class="workspace-details">
                                分支: ${workspace.branch} | 创建时间: ${createTime} | SN: ${workspace.sn}
                            </div>
                        </div>
                        <span class="workspace-status ${statusClass}">${workspace.status}</span>
                    </div>
                `;
            }).join('');

            container.innerHTML = workspaceHTML;
        }

        async function refreshWorkspaces() {
            await fetchWorkspaces();
        }

        async function deleteClosedWorkspaces() {
            if (!confirm('确定要删除所有关闭的工作空间吗？此操作不可撤销。')) {
                return;
            }

            try {
                updateStatus('正在删除关闭的工作空间...');

                const response = await fetch('/api/workspaces/delete-closed', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    updateStatus(`成功删除 ${data.deletedCount} 个关闭的工作空间 - ${new Date().toLocaleString()}`);
                    // 刷新工作空间列表
                    await fetchWorkspaces();
                } else {
                    updateStatus('删除关闭的工作空间失败: ' + data.error);
                }
            } catch (error) {
                updateStatus('删除工作空间时发生网络错误: ' + error.message);
            }
        }

        // 日志管理功能
        async function fetchLogs() {
            try {
                const lines = document.getElementById('lines').value;
                const response = await fetch(`/api/logs?lines=${lines}`);
                const data = await response.json();

                if (data.success) {
                    displayLogs(data.logs);
                    updateStatus(`已加载 ${data.logs.length} 条日志 - ${new Date().toLocaleString()}`);
                } else {
                    updateStatus('获取日志失败: ' + data.error);
                }
            } catch (error) {
                updateStatus('网络错误: ' + error.message);
            }
        }

        function displayLogs(logs) {
            const container = document.getElementById('logContainer');

            if (logs.length === 0) {
                container.innerHTML = '<div class="empty">暂无日志数据</div>';
                return;
            }

            const logHTML = logs.map(log => {
                const match = log.match(/\[(.*?)\]\s*\[(.*?)\]\s*(.*)/);
                if (match) {
                    const [, timestamp, level, message] = match;
                    const levelClass = level.toLowerCase();
                    return `
                        <div class="log-entry ${levelClass}">
                            <span class="timestamp">${timestamp}</span>
                            <span class="level ${levelClass}">[${level}]</span>
                            <span class="message">${message}</span>
                        </div>
                    `;
                } else {
                    return `
                        <div class="log-entry">
                            <span class="message">${log}</span>
                        </div>
                    `;
                }
            }).join('');

            container.innerHTML = logHTML;
            container.scrollTop = container.scrollHeight;
        }

        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }

        function refreshLogs() {
            fetchLogs();
        }

        function toggleAutoRefresh() {
            const button = event.target;

            if (isAutoRefresh) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                isAutoRefresh = false;
                button.textContent = '⏱️ 自动刷新';
                updateStatus('自动刷新已停止');
            } else {
                autoRefreshInterval = setInterval(fetchLogs, 5000);
                isAutoRefresh = true;
                button.textContent = '⏹️ 停止刷新';
                updateStatus('自动刷新已启动 (每5秒)');
            }
        }

        async function clearLogs() {
            try {
                updateStatus('正在清空日志...');

                const response = await fetch('/api/clear-logs', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    document.getElementById('logContainer').innerHTML = '<div class="empty">日志已清空</div>';
                    updateStatus('日志文件已清空 - ' + new Date().toLocaleString());
                } else {
                    updateStatus('清空日志失败: ' + data.error);
                }
            } catch (error) {
                updateStatus('清空日志时发生网络错误: ' + error.message);
            }
        }

        // 页面加载时获取数据
        document.addEventListener('DOMContentLoaded', function () {
            fetchSystemConfig();
            fetchLogs();
            fetchWorkspaces();
        });
    </script>
</body>

</html>