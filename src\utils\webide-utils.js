import { chromium } from 'playwright';
import config from '../config.js';
import { loadCookies, saveScreenshot, getHumanReadableTimestamp, waitForUserInput } from './common-utils.js';
import { info, error } from './logger.js';

/**
 * 创建浏览器实例
 * @returns {Object} browser - 浏览器实例
 */
export async function createBrowser() {
  info('启动浏览器...');
  const browser = await chromium.launch(config.browserOptions);
  return browser;
}

/**
 * 创建新的上下文和页面
 * @param {Object} browser - 浏览器实例
 * @param {string} cookieFile - Cookie 文件路径
 * @param {string} cookiesFromEnv - 环境变量中的cookies
 * @param {string} cfcookieKey - Cloudflare KV 存储中的cookies
 * @returns {Object} { context, page }
 */
export async function createContextAndPage(browser, cookieFile, cookiesFromEnv, cfcookieKey) {
  info('创建新的上下文和页面，重新加载cookies...');
  const context = await browser.newContext();

  // 重新读取并设置cookies（每次都重新加载以防过期）
  const cookies = await loadCookies(cookieFile, cookiesFromEnv, cfcookieKey);
  await context.addCookies(cookies);

  const page = await context.newPage();

  return { context, page };
}



/**
 * 导航到 WebIDE 页面并验证登录状态
 * @param {Object} page - Playwright 页面对象
 */
export async function navigateToWebIDE(page) {
  info('导航到WebIDE页面URL:', config.projectUrl);
  await page.goto(config.projectUrl);

  // 等待页面加载
  await page.waitForTimeout(config.waitTimes.pageLoad);

  info('当前页面URL:', page.url());
  info('页面标题:', await page.title());

  await page.click("button.cloudDevButton")

  // 处理模态对话框
  await page.waitForTimeout(5000);
  const firstPage = page;
  const pages = page.context().pages();
  if (pages.length === 0) {
    error('未找到新打开的页面，继续等待...');
    await page.waitForTimeout(5000);
  }
  page = pages[pages.length - 1];
  if (page != firstPage) {
    await firstPage.close();
  }

  // const webIDEUrl = await getWebIDEUrl();
  // await page.goto(webIDEUrl);

  try {
    //如果是新建的
    //https://cnb.cool/godgodgame/microsoft-reward/-/workspace/loading?sn=cnb-dl8-1isvqh5tl&branch=main&webIDE=false
    await page.waitForURL((url) => {
      return url.href.startsWith(`${config.projectUrl}/-/workspace/loading`);
    }, { timeout: 5000 });

    await page.waitForURL((url) => {
      return url.href.startsWith(`${config.projectUrl}/-/workspace/jump`);
    }, { timeout: 600000 });

    await page.click('.t-loading__parent');
    await page.waitForTimeout(500);
    return page; // 返回当前页面
  }
  catch (err) {
    info('不是新建的工作空间，继续执行...');
  }


  // 检查是否成功登录
  try {
    await page.waitForSelector(config.selectors.editor, {
      timeout: 30000
    });
    info('成功进入WebIDE界面,当前页面URL:', config.projectUrl);
  } catch (err) {
    info('警告: 未检测到编辑器界面，可能需要重新登录');
  }

  return page;
}

/**
 * 处理模态对话框
 * @param {Page} page - Playwright 页面对象
 */

export async function handleWebIDEEnter(page) {
  try {
    // 同时匹配中英文的"打开新环境"/"Open new Workspace"按钮
    const enterButton = await page.waitForSelector('.remote-bg button:has-text("打开新环境"), .remote-bg button:has-text("Open new Workspace")', { timeout: 5000 });
    info('点击"打开新环境/Open new Workspace"按钮');
    await enterButton.click();
    await page.waitForTimeout(5000);
    const pages = page.context().pages();
    if (pages.length === 0) {
      error('未找到新打开的页面，继续等待...');
      await page.waitForTimeout(5000);
    }
    return pages[pages.length - 1]; // 返回当前页面
  } catch (err) {
    info('未发现"打开新环境/Open new Workspace"，继续执行...');
  }

  try {
    // 同时匹配中英文的"重建"/"Rebuild"按钮
    const rebuildButton = await page.waitForSelector('.remote-bg button:has-text("重建"), .remote-bg button:has-text("Rebuild")', { timeout: 30000 });
    info('点击"重建/Rebuild"按钮');
    await rebuildButton.click();
    await page.waitForTimeout(500);
    //使用webIDE打开
    info('等待打开WEBIDE...');
    await page.click('.t-loading__parent', { timeout: 6000000 });
    await page.waitForTimeout(500);
    return page; // 返回当前页面
  }
  catch (err) {
    info('未发现"重建/Rebuild"按钮，继续执行...');
  }

  return page; // 返回当前页面
}



/**
 * 打开终端
 * @param {Object} page - Playwright 页面对象
 * @returns {Object|null} 终端元素或 null
 */
export async function openTerminal(page) {
  // 确保页面获得焦点
  await page.click('body');
  await page.waitForTimeout(5000); // 增加等待时间
  info('尝试打开终端 (Ctrl+~)...');
  // 按下 Ctrl+~ 打开终端
  await page.keyboard.press('Control+`');
  // 等待终端打开
  await page.waitForTimeout(config.waitTimes.terminalOpen);
  // 尝试多种方式查找终端
  const terminalSelectors = config.selectors.terminals;

  let terminalFound = false;
  let terminalElement = null;

  for (const selector of terminalSelectors) {
    try {
      terminalElement = await page.waitForSelector(selector, { timeout: 2000 });
      if (terminalElement) {
        info(`找到终端元素: ${selector}`);
        terminalFound = true;
        break;
      }
    } catch (err) {
      // 继续尝试下一个选择器
    }
  }

  if (!terminalFound) {
    info('未找到终端元素，尝试直接输入命令...');
    return null;
  }

  try {
    // 多次点击确保焦点，并等待终端完全激活
    await terminalElement.click({ timeout: 10000 });
    return terminalElement;
  }
  catch (err) {
    error('点击终端元素时发生错误:', err);
    return null;
  }
}

/**
 * 在终端中执行命令
 * @param {Object} page - Playwright 页面对象
 * @param {string} command - 要执行的命令
 */
export async function executeTerminalCommand(page, command) {
  info(`执行命令: ${command}`);

  // 输入命令
  await page.keyboard.type(command);
  await page.waitForTimeout(500);

  // 按回车执行命令
  await page.keyboard.press('Enter');

  await page.waitForTimeout(config.waitTimes.commandExecution);
  info('命令已执行');
}

/**
 * 完整的命令执行流程
 * @param {Object} page - Playwright 页面对象
 * @param {string} screenshotPrefix - 截图文件名前缀
 * @returns {boolean} 执行是否成功
 */
export async function executeCommandFlow(page, screenshotPrefix = 'screenshot') {
  try {
    // 打开终端
    await openTerminal(page);
    // 执行命令
    await executeTerminalCommand(page, config.command);
  } catch (err) {
    error(`[${getHumanReadableTimestamp()}] 执行命令时发生错误:`, err);
  }
  finally {
    // 截图保存执行结果
    const screenshotDir = config.screenshotDir || './screenshots';
    const screenshotPath = await saveScreenshot(page, screenshotDir, screenshotPrefix);
  }
}



/**
 * 获取 WebIDE URL
 * @returns {Promise<string>} WebIDE URL
 */
/**
 * 获取工作空间列表
 * @returns {Promise<Array>} 工作空间列表
 */
export async function fetchWorkspaceList() {
  try {
    const cookies = await loadCookies(config.cookieFile, config.cookiesFromEnv, config.cloudflareKV.cookieKey);
    const cookieString = cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');

    if (!cookieString) {
      throw new Error('未找到有效的 cookies');
    }

    const response = await fetch('https://cnb.cool/workspace/list?slug=&branch=&page=1&pageSize=20', {
      method: 'GET',
      headers: {
        'accept': 'application/vnd.cnb.web+json',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'cache-control': 'no-cache',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'sec-ch-ua': '"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'cookie': cookieString,
        'Referer': 'https://cnb.cool/u/zhepama/workspaces',
        'Referrer-Policy': 'strict-origin-when-cross-origin'
      }
    });

    if (!response.ok) {
      throw new Error(`获取工作空间列表失败: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.list || [];
  } catch (err) {
    error('获取工作空间列表错误:', err);
    throw err;
  }
}

/**
 * 删除指定的工作空间
 * @param {string} pipelineId - 工作空间的 pipeline ID
 * @returns {Promise<Object>} 删除结果
 */
export async function deleteWorkspace(pipelineId) {
  try {
    const cookies = await loadCookies(config.cookieFile, config.cookiesFromEnv, config.cloudflareKV.cookieKey);
    const cookieString = cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');

    if (!cookieString) {
      throw new Error('未找到有效的 cookies');
    }

    const response = await fetch('https://cnb.cool/workspace/delete', {
      method: 'POST',
      headers: {
        'accept': 'application/vnd.cnb.web+json',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'sec-ch-ua': '"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'cookie': cookieString,
        'Referer': 'https://cnb.cool/u/zhepama/workspaces',
        'Referrer-Policy': 'strict-origin-when-cross-origin'
      },
      body: JSON.stringify({ pipelineId })
    });

    if (!response.ok) {
      throw new Error(`删除工作空间失败: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (err) {
    error(`删除工作空间 ${pipelineId} 错误:`, err);
    throw err;
  }
}

/**
 * 删除所有关闭的工作空间
 * @returns {Promise<Object>} 删除结果统计
 */
export async function deleteClosedWorkspaces() {
  try {
    info('开始删除关闭的工作空间...');

    // 获取所有工作空间
    const workspaces = await fetchWorkspaceList();

    // 筛选出关闭的工作空间
    const closedWorkspaces = workspaces.filter(workspace => workspace.status === 'closed');

    info(`找到 ${closedWorkspaces.length} 个关闭的工作空间`);

    if (closedWorkspaces.length === 0) {
      info('没有找到关闭的工作空间，无需删除');
      return {
        success: true,
        deletedCount: 0,
        totalClosed: 0,
        message: '没有找到关闭的工作空间'
      };
    }

    let deletedCount = 0;
    const errors = [];

    // 逐个删除关闭的工作空间
    for (const workspace of closedWorkspaces) {
      try {
        info(`删除工作空间: ${workspace.slug} (${workspace.pipeline_id})`);
        await deleteWorkspace(workspace.pipeline_id);
        deletedCount++;
        info(`成功删除工作空间: ${workspace.slug}`);
      } catch (err) {
        const errorMsg = `删除工作空间 ${workspace.slug} 失败: ${err.message}`;
        error(errorMsg);
        errors.push(errorMsg);
      }
    }

    const result = {
      success: true,
      deletedCount: deletedCount,
      totalClosed: closedWorkspaces.length,
      timestamp: new Date().toISOString()
    };

    if (errors.length > 0) {
      result.errors = errors;
      result.message = `成功删除 ${deletedCount} 个工作空间，${errors.length} 个失败`;
    } else {
      result.message = `成功删除 ${deletedCount} 个关闭的工作空间`;
    }

    info(`删除操作完成: ${result.message}`);
    return result;

  } catch (err) {
    error('删除关闭工作空间时发生错误:', err);
    throw err;
  }
}

export async function getWebIDEUrl() {
  try {
    // 从配置中读取必要参数
    const { projectUrl, branch, cookieFile, cookiesFromEnv } = config;

    if (!projectUrl) {
      throw new Error('项目 URL 未配置，请在 config.js 中设置 projectUrl 或通过环境变量 PROJECT_URL 设置');
    }

    info('开始获取 WebIDE URL...');
    info(`项目 URL: ${projectUrl}`);
    info(`分支: ${branch}`);

    // 加载 cookies
    const cookies = await loadCookies(cookieFile, cookiesFromEnv, config.cloudflareKV.cookieKey);

    // 将 cookies 数组转换为字符串格式
    const cookieString = cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');

    if (!cookieString) {
      throw new Error('未找到有效的 cookies，请确保已正确配置 cookies');
    }

    // 第一步：检查是否已有现存的 WebIDE URL
    info('检查现有的 WebIDE URL...');
    const latestUrlResponse = await fetch(`${projectUrl}/-/workspace/latest-webide-url?ref=${branch}`, {
      method: 'GET',
      headers: {
        'accept': 'application/vnd.cnb.web+json',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'cache-control': 'no-cache',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'sec-ch-ua': '"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'cookie': cookieString,
        'Referer': projectUrl,
        'Referrer-Policy': 'strict-origin-when-cross-origin'
      }
    });

    if (!latestUrlResponse.ok) {
      throw new Error(`获取现有 WebIDE URL 失败: ${latestUrlResponse.status} ${latestUrlResponse.statusText}`);
    }

    //https://cnb.cool/godgodgame/microsoft-reward/-/workspace/vscode-web/cnb-qao-1isvo7cqg-001
    const latestUrlData = await latestUrlResponse.json();
    info('现有 WebIDE URL 响应:', latestUrlData);

    // 如果已有 URL 且不为空，直接返回
    if (latestUrlData.url && latestUrlData.url.trim() !== '') {
      info(`找到现有的 WebIDE URL: ${latestUrlData.url}`);
      return latestUrlData.url;
    }

    // 第二步：如果没有现有 URL，启动新的工作空间
    info('未找到现有 WebIDE URL，启动新的工作空间...');
    const startResponse = await fetch(`${projectUrl}/-/workspace/start`, {
      method: 'POST',
      headers: {
        'accept': 'application/vnd.cnb.web+json',
        'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'sec-ch-ua': '"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'cookie': cookieString,
        'Referer': projectUrl,
        'Referrer-Policy': 'strict-origin-when-cross-origin'
      },
      body: JSON.stringify({
        ref: `refs/heads/${branch}`,
        branch: branch,
        language: 'zh-CN'
      })
    });

    if (!startResponse.ok) {
      throw new Error(`启动工作空间失败: ${startResponse.status} ${startResponse.statusText}`);
    }

    const startData = await startResponse.json();
    info('启动工作空间响应:', startData);

    // 第三步：构建加载页面 URL
    if (!startData.sn) {
      throw new Error('启动工作空间响应中缺少 sn 字段');
    }

    const loadingUrl = `${projectUrl}/-/workspace/loading?sn=${startData.sn}&branch=${branch}`;
    info(`构建的加载页面 URL: ${loadingUrl}`);

    return loadingUrl;

  } catch (err) {
    error('获取 WebIDE URL 时发生错误:', err);
    throw err;
  }
}