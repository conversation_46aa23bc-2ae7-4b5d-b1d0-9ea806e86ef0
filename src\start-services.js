#!/usr/bin/env node

/**
 * 服务启动脚本
 * 同时启动 Web 服务器和调度器
 */

import { spawn } from 'child_process';
import { info as log, error as logError } from './utils/logger.js';

// 存储子进程
const processes = [];

/**
 * 启动子进程
 */
function startProcess(name, command, args = []) {
  log(`启动 ${name}...`);

  const child = spawn('node', [command, ...args], {
    stdio: 'inherit',
    cwd: process.cwd()
  });

  child.on('error', (error) => {
    logError(`${name} 启动失败:`, error);
  });

  child.on('exit', (code, signal) => {
    if (code !== 0) {
      logError(`${name} 异常退出，代码: ${code}, 信号: ${signal}`);
    } else {
      log(`${name} 正常退出`);
    }
  });

  processes.push({ name, process: child });
  return child;
}

/**
 * 优雅关闭所有进程
 */
function gracefulShutdown() {
  log('收到关闭信号，正在停止所有服务...');

  processes.forEach(({ name, process }) => {
    if (!process.killed) {
      log(`停止 ${name}...`);
      process.kill('SIGTERM');
    }
  });

  // 等待一段时间后强制关闭
  setTimeout(() => {
    processes.forEach(({ name, process }) => {
      if (!process.killed) {
        log(`强制停止 ${name}...`);
        process.kill('SIGKILL');
      }
    });
    process.exit(0);
  }, 5000);
}

/**
 * 主函数
 */
async function main() {
  log('🚀 启动 Cnb Runner 服务');
  log('='.repeat(50));

  try {
    // 启动 Web 服务器
    const webServer = startProcess('Web 服务器', 'src/web-server.js');

    // 等待一下确保 Web 服务器启动
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 启动调度器
    const scheduler = startProcess('调度器', 'src/scheduler.js');

    log('='.repeat(50));
    log('✅ 所有服务已启动');
    log('📊 Web 界面: http://localhost:7860');
    log('⏰ 调度器: 每10分钟执行一次任务');
    log('按 Ctrl+C 停止所有服务');

    // 监听退出信号
    process.on('SIGINT', gracefulShutdown);
    process.on('SIGTERM', gracefulShutdown);

    // 保持主进程运行
    process.on('exit', () => {
      log('主进程退出');
    });

  } catch (error) {
    logError('启动服务失败:', error);
    process.exit(1);
  }
}

// 运行主函数
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const scriptPath = path.resolve(process.argv[1]);

if (path.resolve(__filename) === scriptPath) {
  main().catch(error => {
    logError('服务启动异常:', error);
    process.exit(1);
  });
}

export { main };
